def get_table_name(table_name, zone, table_num):
    if table_num == 0:
        return table_name
    str_num = 0
    length = len(zone)
    for i in range(0, length):
        str_num += ord(zone[i]) * (256**(length -i -1))
    table_index = str_num % table_num
    return '%s_%s' % (table_name, table_index)

USER_TABLE_NUM = 47
zones = ['1', '4', '5', '1005']

print('Table Name Analysis:')
for zone in zones:
    table_name = get_table_name('user', zone, USER_TABLE_NUM)
    str_num = 0
    for i in range(len(zone)):
        str_num += ord(zone[i]) * (256**(len(zone) -i -1))
    table_index = str_num % USER_TABLE_NUM
    print('Zone %s: %s (index=%d)' % (zone, table_name, table_index))
