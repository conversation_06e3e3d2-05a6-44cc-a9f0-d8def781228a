# 游戏合区完整指南

## 📋 目录
1. [合区原理说明](#合区原理说明)
2. [安全检查工具](#安全检查工具)
3. [创建合区表](#创建合区表)
4. [计算区服ID](#计算区服ID)
5. [执行合区操作](#执行合区操作)
6. [问题排查](#问题排查)

---

## 🎯 合区原理说明

### 核心概念
- **区服ID** → **表名映射**：每个区服ID通过算法映射到特定的user表
- **分表策略**：游戏使用47个user表（user_0到user_46）存储用户数据
- **合区目标**：将多个源区服的数据迁移到一个新的目标区服

### 映射算法
```
区服ID → 字符串数值转换 → 取模47 → user_X表
```

---

## 🔍 安全检查工具

### 1. 检查所有表的使用情况
```bash
# 复制到finalshell运行
mysql -h 127.0.0.1 -u root -p'?u[yBH7TmCYDW{a6' -e "
USE SgLocal;
SELECT 'All user tables status:' as info;
SELECT 
    TABLE_NAME as table_name,
    TABLE_ROWS as estimated_rows
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'SgLocal' 
  AND TABLE_NAME LIKE 'user_%' 
  AND TABLE_NAME REGEXP '^user_[0-9]+$'
ORDER BY TABLE_ROWS, TABLE_NAME;
" 2>/dev/null
```

### 2. 检查现有区服使用的表
```bash
# 复制到finalshell运行
python -c "
def get_table_name(zone, table_num=47):
    str_num = 0
    for i, char in enumerate(zone):
        str_num += ord(char) * (256**(len(zone) - i - 1))
    return f'user_{str_num % table_num}'

print('=== 现有区服表映射 ===')
existing_zones = ['1', '4', '5', '6']  # 根据实际情况修改
for zone in existing_zones:
    table = get_table_name(zone)
    print(f'{zone}区 -> {table}')
"
```

---

## 🛠️ 创建合区表

### 方法1：创建指定索引的表
```bash
# 创建user_66表（示例）
mysql -h 127.0.0.1 -u root -p'?u[yBH7TmCYDW{a6' -e "
USE SgLocal;
CREATE TABLE IF NOT EXISTS user_66 (
    uid varchar(32) NOT NULL,
    zone varchar(32) NOT NULL,
    power int(11) DEFAULT NULL,
    user_data longblob,
    PRIMARY KEY (uid,zone),
    KEY zone (zone),
    KEY power (power)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
SELECT 'user_66表创建完成' as result;
SELECT COUNT(*) as record_count FROM user_66;
" 2>/dev/null
```

### 方法2：批量创建多个表
```bash
# 创建user_60到user_70的表
for i in {60..70}; do
mysql -h 127.0.0.1 -u root -p'?u[yBH7TmCYDW{a6' -e "
USE SgLocal;
CREATE TABLE IF NOT EXISTS user_$i (
    uid varchar(32) NOT NULL,
    zone varchar(32) NOT NULL,
    power int(11) DEFAULT NULL,
    user_data longblob,
    PRIMARY KEY (uid,zone),
    KEY zone (zone),
    KEY power (power)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
" 2>/dev/null
echo "user_$i 表创建完成"
done
```

---

## 🎯 计算区服ID

### 1. 根据目标表计算可用区服ID
```bash
# 计算映射到user_66的区服ID
python -c "
def get_table_index(zone, table_num=47):
    str_num = 0
    for i, char in enumerate(zone):
        str_num += ord(char) * (256**(len(zone) - i - 1))
    return str_num % table_num

target_index = 66 % 47  # user_66对应的实际索引
print(f'=== 映射到user_{target_index}的区服ID ===')

# 查找4位数合区ID（推荐）
safe_zones = []
for num in range(1000, 5000):
    zone = str(num)
    if get_table_index(zone) == target_index:
        safe_zones.append(zone)
        if len(safe_zones) >= 20:  # 找到20个就够了
            break

print('推荐的4位数合区ID:')
for i, zone in enumerate(safe_zones[:10]):
    print(f'{i+1:2d}. {zone} -> user_{target_index}')
    
if safe_zones:
    print(f'\\n最佳选择: {safe_zones[0]} -> user_{target_index}')
"
```

### 2. 验证指定区服ID的安全性
```bash
# 验证区服ID是否安全（修改ZONE_ID为你想要的ID）
ZONE_ID="2007"
python -c "
def get_table_name(zone, table_num=47):
    str_num = 0
    for i, char in enumerate(zone):
        str_num += ord(char) * (256**(len(zone) - i - 1))
    return f'user_{str_num % table_num}'

zone = '$ZONE_ID'
table = get_table_name(zone)
print(f'区服ID: {zone}')
print(f'映射表: {table}')
print(f'需要确保 {table} 表存在且为空')
"

# 检查对应表是否存在和为空
mysql -h 127.0.0.1 -u root -p'?u[yBH7TmCYDW{a6' -e "
USE SgLocal;
SELECT 'Table check for zone $ZONE_ID:' as info;
SELECT COUNT(*) as record_count FROM user_XX;  -- 手动替换XX为计算出的表索引
" 2>/dev/null
```

### 3. 一键生成安全的合区方案
```bash
# 一键生成完整的合区方案
python -c "
def get_table_name(zone, table_num=47):
    str_num = 0
    for i, char in enumerate(zone):
        str_num += ord(char) * (256**(len(zone) - i - 1))
    return f'user_{str_num % table_num}'

def get_table_index(zone, table_num=47):
    str_num = 0
    for i, char in enumerate(zone):
        str_num += ord(char) * (256**(len(zone) - i - 1))
    return str_num % table_num

print('=== 自动生成安全合区方案 ===')

# 已使用的表（根据实际情况修改）
used_tables = ['user_2', 'user_3', 'user_5', 'user_6']
print('已使用的表:', used_tables)

# 查找安全的合区ID
safe_options = []
for num in range(2000, 3000):  # 2000-2999范围的4位数ID
    zone = str(num)
    table = get_table_name(zone)
    if table not in used_tables:
        safe_options.append((zone, table))
        if len(safe_options) >= 10:
            break

print('\\n推荐的安全合区方案:')
for i, (zone, table) in enumerate(safe_options):
    print(f'{i+1:2d}. 区服ID: {zone} -> {table}')

if safe_options:
    best_zone, best_table = safe_options[0]
    table_index = int(best_table.split('_')[1])
    print(f'\\n=== 最佳方案 ===')
    print(f'合区ID: {best_zone}')
    print(f'目标表: {best_table}')
    print(f'表索引: {table_index}')
    print(f'\\n创建表命令:')
    print(f'CREATE TABLE IF NOT EXISTS {best_table} (...);')
"
```

---

## 🚀 执行合区操作

### 1. 准备工作检查清单
```bash
# 合区前检查清单
echo "=== 合区前检查清单 ==="
echo "1. 检查源区服数据量:"
mysql -h 127.0.0.1 -u root -p'?u[yBH7TmCYDW{a6' -e "
USE SgLocal;
SELECT '1区用户数' as info, COUNT(*) as count FROM user_2 WHERE zone='1';
SELECT '4区用户数' as info, COUNT(*) as count FROM user_5 WHERE zone='4';
SELECT '5区用户数' as info, COUNT(*) as count FROM user_6 WHERE zone='5';
" 2>/dev/null

echo "2. 检查目标表状态:"
# 这里需要根据你选择的目标表修改
mysql -h 127.0.0.1 -u root -p'?u[yBH7TmCYDW{a6' -e "
USE SgLocal;
SELECT '目标表记录数' as info, COUNT(*) as count FROM user_XX;  -- 替换XX
" 2>/dev/null

echo "3. 检查服务状态:"
ps aux | grep -E "(server.py|manage.py)" | grep -v grep
netstat -tlnp | grep -E "(5001|5004|5005|8500|3352)"
```

### 2. 数据库配置更新
```bash
# 更新zones表配置（修改MERGE_ZONE_ID为你的合区ID）
MERGE_ZONE_ID="2007"
mysql -h 127.0.0.1 -u root -p'?u[yBH7TmCYDW{a6' -e "
USE SgLocal;
-- 插入新的合区配置
INSERT INTO zones (zone_id, zone_name, host, port, active, merge_times) 
VALUES ('$MERGE_ZONE_ID', '${MERGE_ZONE_ID}区', '**************', 5107, 1, 2)
ON DUPLICATE KEY UPDATE 
zone_name='${MERGE_ZONE_ID}区', host='**************', port=5107, active=1, merge_times=2;

-- 验证配置
SELECT zone_id, zone_name, host, port, active, merge_times FROM zones WHERE zone_id='$MERGE_ZONE_ID';
" 2>/dev/null
```

### 3. 更新zone.txt配置文件
```bash
# 备份并更新zone.txt
cp /data/server/trunk/llol/scripts/configs/zone.txt /data/server/trunk/llol/scripts/configs/zone.txt.backup.$(date +%Y%m%d_%H%M%S)

# 添加新区服配置到zone.txt（修改MERGE_ZONE_ID）
MERGE_ZONE_ID="2007"
cat >> /data/server/trunk/llol/scripts/configs/zone.txt << EOF

    '$MERGE_ZONE_ID': [unicode('${MERGE_ZONE_ID}区', 'utf-8'), ('**************', 5107), datetime.datetime(2025,8,5,6,0), 1, 1, '', 0, '', 2],
EOF

echo "zone.txt配置已更新"
```

---

## 🔧 问题排查

### 1. 区服启动失败
```bash
# 检查区服启动日志
tail -20 /data/server/logs/Zone_XXXX.log  # 替换XXXX为区服ID

# 检查配置加载
python -c "
import sys
sys.path.append('/data/server/trunk/llol/src')
import settings
from django.core.management import setup_environ
setup_environ(settings)
from game_lib.logics import game_config

zone_id = 'XXXX'  # 替换为你的区服ID
if zone_id in game_config.zone:
    print(f'✅ {zone_id}区配置已加载')
    print(f'配置: {game_config.zone[zone_id]}')
else:
    print(f'❌ {zone_id}区配置未找到')
"
```

### 2. 端口冲突检查
```bash
# 检查端口占用
netstat -tlnp | grep 5107

# 开放防火墙端口
firewall-cmd --zone=public --add-port=5107/tcp --permanent
firewall-cmd --reload
firewall-cmd --list-ports | grep 5107
```

### 3. 数据迁移验证
```bash
# 验证数据迁移结果
mysql -h 127.0.0.1 -u root -p'?u[yBH7TmCYDW{a6' -e "
USE SgLocal;
SELECT '合区结果验证' as info;
SELECT 'XXXX区用户数' as info, COUNT(*) as count FROM user_YY WHERE zone='XXXX';  -- 替换XXXX和YY
SELECT '总迁移用户数' as info, COUNT(*) as total FROM user_YY WHERE zone='XXXX';
" 2>/dev/null
```

---

## 📝 使用说明

### 快速开始
1. **运行安全检查工具** - 了解当前表使用情况
2. **选择合区方案** - 使用计算工具找到安全的区服ID
3. **创建目标表** - 如果需要的话
4. **更新配置** - 数据库和配置文件
5. **执行合区** - 通过后台管理系统操作

### 注意事项
- 所有命令中的密码 `?u[yBH7TmCYDW{a6}` 请根据实际情况修改
- 区服ID和表名需要根据计算结果替换
- 合区前务必备份重要数据
- 建议在测试环境先验证流程

### 常用替换项
- `ZONE_ID` - 替换为你的目标区服ID
- `user_XX` - 替换为计算出的目标表名
- `5107` - 替换为你的目标端口号

---

*最后更新：2025-08-05*
