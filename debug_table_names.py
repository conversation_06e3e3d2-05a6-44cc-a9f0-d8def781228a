#!/usr/bin/env python
# -*- coding: utf-8 -*-

def get_table_name(table_name, zone, table_num):
    """
    计算表名的函数，与游戏服务器中的逻辑完全一致
    """
    if table_num == 0:
        return table_name

    if type(zone) == int or type(zone) == long:
        return (zone % table_num)
    
    if isinstance(zone, unicode):
        zone = zone.encode('utf-8')
    
    str_num = 0
    length = len(zone) 
    for i in range(0, length):
        str_num += ord(zone[i]) * (256**(length -i -1))
    table_index = (str_num % table_num)
    table_name = '%s_%s' % (table_name, table_index)
    
    return table_name

# 测试各区的表名计算
USER_TABLE_NUM = 47

zones = ['1', '4', '5', '1005']

print("=== Table Name Analysis ===")
print("USER_TABLE_NUM:", USER_TABLE_NUM)
print()

for zone in zones:
    table_name = get_table_name('user', zone, USER_TABLE_NUM)

    # Calculate detailed process
    str_num = 0
    length = len(zone)
    for i in range(0, length):
        str_num += ord(zone[i]) * (256**(length -i -1))
    table_index = str_num % USER_TABLE_NUM

    print("Zone %s:" % zone)
    print("  - String length: %d" % length)
    print("  - String numeric value: %d" % str_num)
    print("  - Table index: %d" % table_index)
    print("  - Table name: %s" % table_name)
    print()

print("=== Analysis Results ===")
print("Zone 1 table:", get_table_name('user', '1', USER_TABLE_NUM))
print("Zone 4 table:", get_table_name('user', '4', USER_TABLE_NUM))
print("Zone 5 table:", get_table_name('user', '5', USER_TABLE_NUM))
print("Zone 1005 table:", get_table_name('user', '1005', USER_TABLE_NUM))
