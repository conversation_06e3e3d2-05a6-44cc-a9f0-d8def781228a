# 恢复数据库备份
mysql -u root -p SgLocal < /data/sql/beifen_fixed.sql

### 端口分配规则
- **1、4、5区**: 保持原端口 5001、5004、5005 (1合区特殊处理)
- **2合区**: 5100-5199 (如1005区→5105)
- **3合区**: 5200-5299 (如2005区→5205)
- **4合区**: 5300-5399 (如3005区→5305)

### 区服ID编码
- **1合区**: 1, 4, 5 (纯数字，特殊处理)
- **2合区**: 1005 (1000 + 基础区号)
- **3合区**: 2005 (2000 + 基础区号)
- **兼容格式**: h1_5, h2_5 等传统格式

### 代码逻辑
- 同时支持纯数字和h{n}_{id}格式
- local环境专门的端口分配逻辑
- 统一的合区次数识别算法


# 3. 启动区服测试
./start.sh

# 4. 检查端口占用
netstat -an | findstr "5001\|5004\|5005"
```

## 📋 文件完整路径清单

```
必须替换的文件：
├── server/trunk/game_lib/game_lib/models/main.py
├── server/service/join_user_zone/join_user_by_zone.py
└── start.sh
server/trunk/admin/admin/templates/admin/merge_zone/merge_zone.html
 

测试服删除数据库导入备份
# 删除并重建SgLocal数据库
mysql -u root -p -e "
DROP DATABASE IF EXISTS SgLocal;
CREATE DATABASE SgLocal CHARACTER SET utf8 COLLATE utf8_general_ci;
"
# 导入备份文件
mysql -u root -p SgLocal < /data/sql/beifen_fixed.sql
# 验证数据恢复情况
mysql -u root -p -e "
USE SgLocal;
SELECT 'user_2' as table_name, zone, COUNT(*) as count FROM user_2 GROUP BY zone
UNION ALL
SELECT 'user_5' as table_name, zone, COUNT(*) as count FROM user_5 GROUP BY zone
UNION ALL
SELECT 'user_6' as table_name, zone, COUNT(*) as count FROM user_6 GROUP BY zone;
"

**重要提醒**: 这些修改主要影响端口分配逻辑，不会影响现有区服的正常运行。1、4、5区将继续使用原端口，只有新的合区才会使用新的端口分配规则。

合区前需要提前以下几个步骤
点击合服前要确保3352端口监听  服务也要跑起来,然后才能点合服
# 检查是否有服务管理进程在运行
ps aux | grep server_manage

# 如果没有，启动它
cd /data/server/service/
nohup python server_manage.py > server_manage.log 2>&1 &

# 检查端口是否监听
netstat -tlnp | grep 3352

